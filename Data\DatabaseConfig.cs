using System;
using System.IO;

namespace DriverManagementSystem.Data
{
    /// <summary>
    /// إعدادات قاعدة البيانات - SQLite محلي
    /// </summary>
    public static class DatabaseConfig
    {
        /// <summary>
        /// الحصول على connection string لـ SQLite
        /// </summary>
        public static string GetConnectionString()
        {
            // إنشاء مجلد Data إذا لم يكن موجوداً
            var dataFolder = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Data");
            if (!Directory.Exists(dataFolder))
            {
                Directory.CreateDirectory(dataFolder);
            }

            // مسار قاعدة البيانات المحلية
            var dbPath = Path.Combine(dataFolder, "SFDSYS.db");
            return $"Data Source={dbPath}";
        }

        /// <summary>
        /// التحقق من وجود قاعدة البيانات
        /// </summary>
        public static bool DatabaseExists()
        {
            var dataFolder = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Data");
            var dbPath = Path.Combine(dataFolder, "SFDSYS.db");
            return File.Exists(dbPath);
        }

        /// <summary>
        /// الحصول على مسار قاعدة البيانات
        /// </summary>
        public static string GetDatabasePath()
        {
            var dataFolder = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Data");
            return Path.Combine(dataFolder, "SFDSYS.db");
        }
    }
}
        {
            try
            {
                if (File.Exists(ConfigFilePath))
                {
                    var lines = File.ReadAllLines(ConfigFilePath);
                    var settings = new DatabaseSettings();

                    foreach (var line in lines)
                    {
                        var parts = line.Split('=');
                        if (parts.Length == 2)
                        {
                            var key = parts[0].Trim();
                            var value = parts[1].Trim();

                            switch (key)
                            {
                                case "ServerName":
                                    settings.ServerName = value;
                                    break;
                                case "DatabaseName":
                                    settings.DatabaseName = value;
                                    break;
                                case "UseWindowsAuth":
                                    settings.UseWindowsAuth = bool.Parse(value);
                                    break;
                                case "Username":
                                    settings.Username = value;
                                    break;
                                case "Password":
                                    settings.Password = value;
                                    break;
                            }
                        }
                    }

                    return settings;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحميل الإعدادات: {ex.Message}");
            }

            return new DatabaseSettings();
        }

        /// <summary>
        /// حفظ الإعدادات في الملف
        /// </summary>
        public static void SaveSettings(DatabaseSettings settings)
        {
            try
            {
                var lines = new[]
                {
                    $"ServerName={settings.ServerName}",
                    $"DatabaseName={settings.DatabaseName}",
                    $"UseWindowsAuth={settings.UseWindowsAuth}",
                    $"Username={settings.Username}",
                    $"Password={settings.Password}"
                };

                File.WriteAllLines(ConfigFilePath, lines);
                System.Diagnostics.Debug.WriteLine($"✅ تم حفظ الإعدادات في: {ConfigFilePath}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في حفظ الإعدادات: {ex.Message}");
            }
        }

        /// <summary>
        /// التحقق من وجود ملف الإعدادات
        /// </summary>
        public static bool HasConfigFile()
        {
            return File.Exists(ConfigFilePath);
        }

        /// <summary>
        /// حذف ملف الإعدادات
        /// </summary>
        public static void DeleteConfigFile()
        {
            try
            {
                if (File.Exists(ConfigFilePath))
                {
                    File.Delete(ConfigFilePath);
                    System.Diagnostics.Debug.WriteLine("✅ تم حذف ملف الإعدادات");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في حذف ملف الإعدادات: {ex.Message}");
            }
        }
    }
}
