using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Data;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using DriverManagementSystem.Data;
using DriverManagementSystem.Services;

namespace DriverManagementSystem.Views
{
    /// <summary>
    /// واجهة إعداد اتصال قاعدة البيانات الاحترافية
    /// </summary>
    public partial class DatabaseConnectionWindow : Window, INotifyPropertyChanged
    {
        public event PropertyChangedEventHandler? PropertyChanged;

        private ObservableCollection<TableInfo> _tables = new();
        public ObservableCollection<TableInfo> Tables
        {
            get => _tables;
            set
            {
                _tables = value;
                PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(nameof(Tables)));
            }
        }

        public bool ConnectionSuccessful { get; private set; }
        public string ConnectionString { get; private set; } = string.Empty;

        public DatabaseConnectionWindow()
        {
            InitializeComponent();
            TablesDataGrid.ItemsSource = Tables;
            LoadCurrentSettings();
            SetupEventHandlers();
        }

        private void LoadCurrentSettings()
        {
            try
            {
                // تحميل الإعدادات الحالية
                ServerNameTextBox.Text = Environment.GetEnvironmentVariable("SQL_SERVER_NAME") ?? "localhost";
                DatabaseNameTextBox.Text = Environment.GetEnvironmentVariable("SQL_DATABASE_NAME") ?? "SFDSYS";

                var useWindowsAuth = Environment.GetEnvironmentVariable("SQL_USE_WINDOWS_AUTH");
                if (useWindowsAuth?.ToLower() == "false")
                {
                    SqlAuthRadio.IsChecked = true;
                    UsernameTextBox.Text = Environment.GetEnvironmentVariable("SQL_USERNAME") ?? "";
                }
                else
                {
                    WindowsAuthRadio.IsChecked = true;
                }
            }
            catch (Exception ex)
            {
                UpdateConnectionStatus($"❌ خطأ في تحميل الإعدادات: {ex.Message}", false);
            }
        }

        /// <summary>
        /// إعادة تعيين جميع إعدادات قاعدة البيانات
        /// </summary>
        public static void ResetDatabaseSettings()
        {
            Environment.SetEnvironmentVariable("SQL_SERVER_NAME", null);
            Environment.SetEnvironmentVariable("SQL_DATABASE_NAME", null);
            Environment.SetEnvironmentVariable("SQL_USE_WINDOWS_AUTH", null);
            Environment.SetEnvironmentVariable("SQL_USERNAME", null);
        }

        private void SetupEventHandlers()
        {
            WindowsAuthRadio.Checked += (s, e) => ToggleAuthenticationFields(false);
            SqlAuthRadio.Checked += (s, e) => ToggleAuthenticationFields(true);
        }

        private void ToggleAuthenticationFields(bool enableSqlAuth)
        {
            UsernameTextBox.IsEnabled = enableSqlAuth;
            PasswordBox.IsEnabled = enableSqlAuth;
            UsernameLabel.Opacity = enableSqlAuth ? 1.0 : 0.5;
            PasswordLabel.Opacity = enableSqlAuth ? 1.0 : 0.5;
        }

        private async void TestConnectionButton_Click(object sender, RoutedEventArgs e)
        {
            await TestConnection();
        }

        private async Task TestConnection()
        {
            try
            {
                UpdateConnectionStatus("⏳ جاري اختبار الاتصال...", null);
                ConnectionProgressBar.Visibility = Visibility.Visible;
                ConnectionProgressBar.IsIndeterminate = true;

                var connectionString = BuildConnectionString();
                
                using var connection = new SqlConnection(connectionString);
                await connection.OpenAsync();
                
                // اختبار إنشاء قاعدة البيانات إذا لم تكن موجودة
                await EnsureDatabaseExists(connection);
                
                ConnectionString = connectionString;
                ConnectionSuccessful = true;
                
                UpdateConnectionStatus("✅ تم الاتصال بنجاح!", true);
                
                // تحديث قائمة الجداول
                await RefreshTables();
            }
            catch (Exception ex)
            {
                ConnectionSuccessful = false;
                UpdateConnectionStatus($"❌ فشل الاتصال: {ex.Message}", false);
            }
            finally
            {
                ConnectionProgressBar.Visibility = Visibility.Collapsed;
                ConnectionProgressBar.IsIndeterminate = false;
            }
        }

        private string BuildConnectionString()
        {
            var builder = new SqlConnectionStringBuilder();

            builder.DataSource = ServerNameTextBox.Text.Trim();
            builder.InitialCatalog = DatabaseNameTextBox.Text.Trim();
            builder.ConnectTimeout = 30;
            builder.CommandTimeout = 60;
            builder.TrustServerCertificate = true;

            if (WindowsAuthRadio.IsChecked == true)
            {
                builder.IntegratedSecurity = true;
            }
            else
            {
                builder.UserID = UsernameTextBox.Text.Trim();
                builder.Password = PasswordBox.Password;
            }

            return builder.ConnectionString;
        }

        private string BuildConnectionString(Data.DatabaseConfig.DatabaseSettings settings)
        {
            var builder = new SqlConnectionStringBuilder();

            builder.DataSource = settings.ServerName;
            builder.InitialCatalog = settings.DatabaseName;
            builder.ConnectTimeout = 30;
            builder.CommandTimeout = 60;
            builder.TrustServerCertificate = true;

            if (settings.UseWindowsAuth)
            {
                builder.IntegratedSecurity = true;
            }
            else
            {
                builder.UserID = settings.Username;
                builder.Password = settings.Password;
            }

            return builder.ConnectionString;
        }

        private async Task<(bool Success, string ErrorMessage)> TestConnectionAsync(string connectionString)
        {
            try
            {
                using var connection = new SqlConnection(connectionString);
                await connection.OpenAsync();
                return (true, string.Empty);
            }
            catch (Exception ex)
            {
                return (false, ex.Message);
            }
        }

        private async Task EnsureDatabaseExists(SqlConnection connection)
        {
            var databaseName = DatabaseNameTextBox.Text.Trim();

            // التحقق من وجود قاعدة البيانات
            var checkDbQuery = $"SELECT COUNT(*) FROM sys.databases WHERE name = '{databaseName}'";
            using var checkCmd = new SqlCommand(checkDbQuery, connection);
            var dbExists = (int)await checkCmd.ExecuteScalarAsync() > 0;

            if (!dbExists)
            {
                // إنشاء قاعدة البيانات
                var createDbQuery = $"CREATE DATABASE [{databaseName}]";
                using var createCmd = new SqlCommand(createDbQuery, connection);
                await createCmd.ExecuteNonQueryAsync();

                UpdateConnectionStatus($"✅ تم إنشاء قاعدة البيانات '{databaseName}' بنجاح!", true);
            }
            else
            {
                UpdateConnectionStatus($"✅ تم العثور على قاعدة البيانات '{databaseName}'", true);
            }
        }

        private async void RefreshTablesButton_Click(object sender, RoutedEventArgs e)
        {
            await RefreshTables();
        }

        private async Task RefreshTables()
        {
            if (!ConnectionSuccessful)
            {
                MessageBox.Show("يرجى اختبار الاتصال أولاً", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            try
            {
                Tables.Clear();
                TablesCountText.Text = "⏳ جاري تحديث القائمة...";

                using var connection = new SqlConnection(ConnectionString);
                await connection.OpenAsync();

                // الحصول على قائمة الجداول
                var tablesQuery = @"
                    SELECT
                        TABLE_NAME as TableName
                    FROM INFORMATION_SCHEMA.TABLES
                    WHERE TABLE_TYPE = 'BASE TABLE'
                    ORDER BY TABLE_NAME";

                using var cmd = new SqlCommand(tablesQuery, connection);
                using var reader = await cmd.ExecuteReaderAsync();

                while (await reader.ReadAsync())
                {
                    var tableName = reader.GetString("TableName");

                    Tables.Add(new TableInfo
                    {
                        TableName = tableName,
                        RowCount = 0, // سيتم حساب العدد لاحقاً
                        Status = "جاري التحقق..."
                    });
                }

                reader.Close();

                // حساب عدد الصفوف لكل جدول
                foreach (var table in Tables)
                {
                    try
                    {
                        using var countCmd = new SqlCommand($"SELECT COUNT(*) FROM [{table.TableName}]", connection);
                        var result = await countCmd.ExecuteScalarAsync();
                        table.RowCount = Convert.ToInt64(result);
                        table.Status = table.RowCount > 0 ? "يحتوي على بيانات" : "فارغ";
                    }
                    catch
                    {
                        table.RowCount = 0;
                        table.Status = "خطأ في القراءة";
                    }
                }

                TablesCountText.Text = $"عدد الجداول: {Tables.Count}";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحديث قائمة الجداول: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
                TablesCountText.Text = "خطأ في التحديث";
            }
        }

        private async void CreateDatabaseButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // إنشاء كائن الإعدادات من النموذج
                var settings = new Data.DatabaseConfig.DatabaseSettings
                {
                    ServerName = ServerNameTextBox.Text.Trim(),
                    DatabaseName = DatabaseNameTextBox.Text.Trim(),
                    UseWindowsAuth = WindowsAuthRadio.IsChecked == true,
                    Username = UsernameTextBox.Text.Trim(),
                    Password = PasswordBox.Password
                };

                // إنشاء قاعدة البيانات إذا لم تكن موجودة
                await CreateDatabaseIfNotExists(settings);

                UpdateConnectionStatus("⏳ جاري إنشاء الجداول...", null);

                var connectionString = BuildConnectionString(settings);
                var options = new DbContextOptionsBuilder<ApplicationDbContext>()
                    .UseSqlServer(connectionString)
                    .Options;

                using var context = new ApplicationDbContext(options);

                // إنشاء الجداول
                await context.Database.EnsureCreatedAsync();

                UpdateConnectionStatus("✅ تم إنشاء قاعدة البيانات والجداول بنجاح!", true);

                // تحديث قائمة الجداول
                await RefreshTables();

                ConnectionSuccessful = true;
                ConnectionString = connectionString;

                MessageBox.Show("✅ تم إنشاء قاعدة البيانات والجداول بنجاح!", "نجح",
                    MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                UpdateConnectionStatus($"❌ فشل إنشاء قاعدة البيانات: {ex.Message}", false);
                MessageBox.Show($"❌ خطأ في إنشاء قاعدة البيانات: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void SaveButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // إنشاء كائن الإعدادات
                var settings = new Data.DatabaseConfig.DatabaseSettings
                {
                    ServerName = ServerNameTextBox.Text.Trim(),
                    DatabaseName = DatabaseNameTextBox.Text.Trim(),
                    UseWindowsAuth = WindowsAuthRadio.IsChecked == true,
                    Username = UsernameTextBox.Text.Trim(),
                    Password = PasswordBox.Password
                };

                // إذا لم يتم اختبار الاتصال، اختبره الآن
                if (!ConnectionSuccessful)
                {
                    ConnectionStatusText.Text = "⏳ جاري اختبار الاتصال...";
                    ConnectionProgressBar.Visibility = Visibility.Visible;
                    ConnectionProgressBar.IsIndeterminate = true;

                    await Task.Delay(500); // تأخير بصري

                    var connectionString = BuildConnectionString(settings);
                    var testResult = await TestConnectionAsync(connectionString);

                    ConnectionProgressBar.Visibility = Visibility.Collapsed;

                    if (!testResult.Success)
                    {
                        // إذا فشل الاتصال، اسأل المستخدم إذا كان يريد إنشاء قاعدة البيانات
                        var result = MessageBox.Show(
                            $"فشل الاتصال بقاعدة البيانات:\n{testResult.ErrorMessage}\n\nهل تريد إنشاء قاعدة البيانات تلقائياً؟",
                            "فشل الاتصال",
                            MessageBoxButton.YesNo,
                            MessageBoxImage.Question);

                        if (result == MessageBoxResult.Yes)
                        {
                            await CreateDatabaseIfNotExists(settings);
                        }
                        else
                        {
                            return;
                        }
                    }
                }

                // حفظ الإعدادات في الملف
                Data.DatabaseConfig.SaveSettings(settings);

                MessageBox.Show("✅ تم حفظ الإعدادات بنجاح!", "نجح",
                    MessageBoxButton.OK, MessageBoxImage.Information);

                DialogResult = true;
                Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"❌ خطأ في حفظ الإعدادات: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async Task CreateDatabaseIfNotExists(Data.DatabaseConfig.DatabaseSettings settings)
        {
            try
            {
                ConnectionStatusText.Text = "🏗️ جاري إنشاء قاعدة البيانات...";
                ConnectionProgressBar.Visibility = Visibility.Visible;
                ConnectionProgressBar.IsIndeterminate = true;

                // إنشاء connection string للخادم فقط (بدون اسم قاعدة البيانات)
                var masterConnectionString = BuildConnectionString(new Data.DatabaseConfig.DatabaseSettings
                {
                    ServerName = settings.ServerName,
                    DatabaseName = "master", // الاتصال بقاعدة البيانات الرئيسية
                    UseWindowsAuth = settings.UseWindowsAuth,
                    Username = settings.Username,
                    Password = settings.Password
                });

                using var connection = new SqlConnection(masterConnectionString);
                await connection.OpenAsync();

                // فحص وجود قاعدة البيانات
                var checkDbCommand = new SqlCommand($"SELECT COUNT(*) FROM sys.databases WHERE name = '{settings.DatabaseName}'", connection);
                var dbExists = (int)await checkDbCommand.ExecuteScalarAsync() > 0;

                if (!dbExists)
                {
                    // إنشاء قاعدة البيانات
                    var createDbCommand = new SqlCommand($"CREATE DATABASE [{settings.DatabaseName}]", connection);
                    await createDbCommand.ExecuteNonQueryAsync();

                    ConnectionStatusText.Text = "✅ تم إنشاء قاعدة البيانات بنجاح";
                }
                else
                {
                    ConnectionStatusText.Text = "ℹ️ قاعدة البيانات موجودة مسبقاً";
                }

                ConnectionProgressBar.Visibility = Visibility.Collapsed;
                ConnectionSuccessful = true;

                // اختبار الاتصال مرة أخرى
                var testConnectionString = BuildConnectionString(settings);
                var testResult = await TestConnectionAsync(testConnectionString);
                if (testResult.Success)
                {
                    ConnectionString = testConnectionString;
                    UpdateConnectionStatus("✅ تم الاتصال بقاعدة البيانات بنجاح!", true);
                }
            }
            catch (Exception ex)
            {
                ConnectionProgressBar.Visibility = Visibility.Collapsed;
                ConnectionStatusText.Text = $"❌ فشل إنشاء قاعدة البيانات: {ex.Message}";
                throw;
            }
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }

        private void UpdateConnectionStatus(string message, bool? success)
        {
            ConnectionStatusText.Text = message;
            
            if (success.HasValue)
            {
                ConnectionStatusText.Foreground = success.Value ? 
                    System.Windows.Media.Brushes.Green : 
                    System.Windows.Media.Brushes.Red;
            }
            else
            {
                ConnectionStatusText.Foreground = System.Windows.Media.Brushes.Orange;
            }
        }
    }

    /// <summary>
    /// معلومات الجدول
    /// </summary>
    public class TableInfo
    {
        public string TableName { get; set; } = string.Empty;
        public long RowCount { get; set; }
        public string Status { get; set; } = string.Empty;
    }
}
